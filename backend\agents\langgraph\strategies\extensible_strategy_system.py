"""
Extensible Strategy System for LangGraph-based Datagenius System.

This module provides a completely extensible, configuration-driven strategy system
that avoids hardcoded values and allows dynamic loading of persona behaviors.
"""

import logging
import importlib
import inspect
from typing import Dict, Any, List, Optional, Type, Callable
from pathlib import Path
import yaml
import json
from abc import ABC, abstractmethod

from ..states.unified_state import UnifiedDatageniusState

logger = logging.getLogger(__name__)


class ExtensiblePersonaStrategy(ABC):
    """
    Base class for extensible persona strategies.
    
    All behavior is configuration-driven with no hardcoded values.
    Strategies are loaded dynamically based on configuration.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize the extensible strategy."""
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        
        # Load all configuration dynamically
        self.capabilities = config.get("capabilities", [])
        self.supported_intents = config.get("supported_intents", [])
        self.tools = config.get("tools", [])
        self.processing_rules = config.get("processing_rules", {})
        self.prompt_templates = config.get("prompt_templates", {})
        self.methodology_config = config.get("methodology", {})
        self.custom_handlers = config.get("custom_handlers", {})
        
        # Load custom processing functions if specified
        self.custom_processors = self._load_custom_processors()
        
    def _load_custom_processors(self) -> Dict[str, Callable]:
        """Load custom processing functions from configuration."""
        processors = {}
        
        for processor_name, processor_config in self.custom_handlers.items():
            try:
                module_path = processor_config.get("module")
                function_name = processor_config.get("function")
                
                if module_path and function_name:
                    module = importlib.import_module(module_path)
                    processor_func = getattr(module, function_name)
                    processors[processor_name] = processor_func
                    
            except Exception as e:
                self.logger.error(f"Error loading custom processor {processor_name}: {e}")
        
        return processors
    
    @abstractmethod
    async def process_message(
        self, 
        state: UnifiedDatageniusState,
        context: Dict[str, Any]
    ) -> UnifiedDatageniusState:
        """Process message using configuration-driven logic."""
        pass
    
    def get_system_prompt(self, context: Dict[str, Any]) -> str:
        """Get system prompt from configuration templates."""
        # Use template from configuration
        template_name = self.prompt_templates.get("system_prompt", "default")
        
        if template_name in self.prompt_templates:
            template = self.prompt_templates[template_name]
            
            # Replace placeholders with context values
            return self._render_template(template, context)
        
        # Fallback to basic prompt
        return self.config.get("default_prompt", "You are a helpful AI assistant.")
    
    def _render_template(self, template: str, context: Dict[str, Any]) -> str:
        """Render template with context values."""
        try:
            # Simple template rendering - can be extended with Jinja2 if needed
            rendered = template
            
            for key, value in context.items():
                placeholder = f"{{{key}}}"
                if placeholder in rendered:
                    rendered = rendered.replace(placeholder, str(value))
            
            return rendered
            
        except Exception as e:
            self.logger.error(f"Error rendering template: {e}")
            return template
    
    def get_specialized_tools(self) -> List[str]:
        """Get specialized tools from configuration."""
        return self.tools
    
    def can_handle_intent(self, intent: str) -> bool:
        """Check if strategy can handle intent based on configuration."""
        return intent in self.supported_intents
    
    def get_capability_score(self, request_context: Dict[str, Any]) -> float:
        """Calculate capability score using configuration-driven rules."""
        scoring_rules = self.processing_rules.get("capability_scoring", {})
        
        # Default scoring based on intent matching
        intent = request_context.get("detected_intent", "")
        base_score = 0.8 if self.can_handle_intent(intent) else 0.1
        
        # Apply custom scoring rules if configured
        for rule_name, rule_config in scoring_rules.items():
            try:
                score_modifier = self._apply_scoring_rule(rule_config, request_context)
                base_score = min(1.0, base_score + score_modifier)
            except Exception as e:
                self.logger.error(f"Error applying scoring rule {rule_name}: {e}")
        
        return base_score
    
    def _apply_scoring_rule(self, rule_config: Dict[str, Any], context: Dict[str, Any]) -> float:
        """Apply a single scoring rule."""
        rule_type = rule_config.get("type", "keyword_match")
        
        if rule_type == "keyword_match":
            keywords = rule_config.get("keywords", [])
            message = context.get("message", "").lower()
            matches = sum(1 for keyword in keywords if keyword.lower() in message)
            return matches * rule_config.get("score_per_match", 0.1)
        
        elif rule_type == "capability_match":
            capabilities = rule_config.get("capabilities", [])
            request_capabilities = context.get("required_capabilities", [])
            matches = len(set(capabilities) & set(request_capabilities))
            return matches * rule_config.get("score_per_match", 0.2)
        
        return 0.0


class ConfigurablePersonaStrategy(ExtensiblePersonaStrategy):
    """
    Fully configurable persona strategy that implements behavior
    entirely through configuration without hardcoded logic.
    """
    
    async def process_message(
        self, 
        state: UnifiedDatageniusState,
        context: Dict[str, Any]
    ) -> UnifiedDatageniusState:
        """Process message using configuration-driven workflow."""
        self.logger.info(f"Processing with configurable strategy: {self.config.get('name', 'unknown')}")
        
        # Apply processing pipeline from configuration
        pipeline = self.processing_rules.get("processing_pipeline", [])
        
        for step in pipeline:
            try:
                state = await self._execute_processing_step(step, state, context)
            except Exception as e:
                self.logger.error(f"Error in processing step {step.get('name', 'unknown')}: {e}")
        
        return state
    
    async def _execute_processing_step(
        self,
        step_config: Dict[str, Any],
        state: UnifiedDatageniusState,
        context: Dict[str, Any]
    ) -> UnifiedDatageniusState:
        """Execute a single processing step from configuration."""
        step_type = step_config.get("type")
        step_name = step_config.get("name", "unknown")
        
        if step_type == "context_extraction":
            # Extract context based on configuration
            extraction_rules = step_config.get("extraction_rules", {})
            for key, rule in extraction_rules.items():
                extracted_value = self._extract_context_value(rule, context, state)
                state.workflow_context[key] = extracted_value
        
        elif step_type == "methodology_application":
            # Apply methodology framework from configuration
            methodology = step_config.get("methodology", {})
            state = await self._apply_methodology(methodology, state, context)
        
        elif step_type == "custom_processor":
            # Execute custom processor function
            processor_name = step_config.get("processor")
            if processor_name in self.custom_processors:
                processor_func = self.custom_processors[processor_name]
                state = await processor_func(state, context, step_config)
        
        elif step_type == "state_update":
            # Update state based on configuration
            updates = step_config.get("updates", {})
            for key, value in updates.items():
                if isinstance(value, str) and value.startswith("${"):
                    # Dynamic value from context
                    context_key = value[2:-1]  # Remove ${ and }
                    value = context.get(context_key, value)
                state.workflow_context[key] = value
        
        return state
    
    def _extract_context_value(
        self,
        rule: Dict[str, Any],
        context: Dict[str, Any],
        state: UnifiedDatageniusState
    ) -> Any:
        """Extract context value based on extraction rule."""
        rule_type = rule.get("type", "direct")
        
        if rule_type == "direct":
            return context.get(rule.get("key"), rule.get("default"))
        
        elif rule_type == "state_lookup":
            return state.workflow_context.get(rule.get("key"), rule.get("default"))
        
        elif rule_type == "computed":
            # Simple computed values - can be extended
            computation = rule.get("computation", {})
            if computation.get("type") == "count":
                items = context.get(computation.get("items_key"), [])
                return len(items)
        
        return rule.get("default")
    
    async def _apply_methodology(
        self,
        methodology: Dict[str, Any],
        state: UnifiedDatageniusState,
        context: Dict[str, Any]
    ) -> UnifiedDatageniusState:
        """Apply methodology framework from configuration."""
        framework_type = methodology.get("type")
        
        if framework_type == "stage_based":
            stages = methodology.get("stages", [])
            current_stage = state.workflow_context.get("current_stage", stages[0] if stages else "default")
            
            # Find current stage configuration
            stage_config = None
            for stage in stages:
                if stage.get("name") == current_stage:
                    stage_config = stage
                    break
            
            if stage_config:
                # Apply stage-specific processing
                stage_actions = stage_config.get("actions", [])
                for action in stage_actions:
                    state = await self._execute_stage_action(action, state, context)
                
                # Determine next stage
                next_stage = stage_config.get("next_stage")
                if next_stage:
                    state.workflow_context["current_stage"] = next_stage
        
        return state
    
    async def _execute_stage_action(
        self,
        action: Dict[str, Any],
        state: UnifiedDatageniusState,
        context: Dict[str, Any]
    ) -> UnifiedDatageniusState:
        """Execute a stage action from methodology configuration."""
        action_type = action.get("type")
        
        if action_type == "set_context":
            key = action.get("key")
            value = action.get("value")
            if key and value is not None:
                state.workflow_context[key] = value
        
        elif action_type == "validate_condition":
            condition = action.get("condition", {})
            if self._evaluate_condition(condition, state, context):
                state.workflow_context[action.get("result_key", "condition_met")] = True
        
        return state
    
    def _evaluate_condition(
        self,
        condition: Dict[str, Any],
        state: UnifiedDatageniusState,
        context: Dict[str, Any]
    ) -> bool:
        """Evaluate a condition from configuration."""
        condition_type = condition.get("type", "exists")
        
        if condition_type == "exists":
            key = condition.get("key")
            source = condition.get("source", "context")
            
            if source == "context":
                return key in context
            elif source == "state":
                return key in state.workflow_context
        
        elif condition_type == "equals":
            key = condition.get("key")
            expected_value = condition.get("value")
            source = condition.get("source", "context")
            
            if source == "context":
                return context.get(key) == expected_value
            elif source == "state":
                return state.workflow_context.get(key) == expected_value
        
        return False


class ExtensibleStrategyRegistry:
    """
    Registry for managing extensible persona strategies.
    
    Loads strategies dynamically from configuration files and modules.
    """
    
    def __init__(self, config_dir: Optional[Path] = None):
        """Initialize the extensible strategy registry."""
        self.logger = logging.getLogger(__name__)
        
        # Configuration directory
        self.config_dir = config_dir or Path(__file__).parent.parent / "config" / "strategies"
        
        # Strategy configurations
        self.strategy_configs: Dict[str, Dict[str, Any]] = {}
        
        # Strategy classes cache
        self.strategy_classes: Dict[str, Type[ExtensiblePersonaStrategy]] = {}
        
        # Strategy instances cache
        self.strategy_instances: Dict[str, ExtensiblePersonaStrategy] = {}
        
        # Load configurations
        self._load_strategy_configurations()
        
        self.logger.info("ExtensibleStrategyRegistry initialized")
    
    def _load_strategy_configurations(self) -> None:
        """Load strategy configurations from files."""
        if not self.config_dir.exists():
            self.logger.warning(f"Strategy config directory not found: {self.config_dir}")
            return
        
        # Load YAML configuration files
        for config_file in self.config_dir.glob("*.yaml"):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f)
                
                strategy_id = config_file.stem
                self.strategy_configs[strategy_id] = config
                
                self.logger.info(f"Loaded strategy configuration: {strategy_id}")
                
            except Exception as e:
                self.logger.error(f"Error loading strategy config {config_file}: {e}")
        
        # Load JSON configuration files
        for config_file in self.config_dir.glob("*.json"):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                strategy_id = config_file.stem
                self.strategy_configs[strategy_id] = config
                
                self.logger.info(f"Loaded strategy configuration: {strategy_id}")
                
            except Exception as e:
                self.logger.error(f"Error loading strategy config {config_file}: {e}")
    
    def get_strategy(
        self,
        strategy_id: str,
        config_override: Optional[Dict[str, Any]] = None
    ) -> ExtensiblePersonaStrategy:
        """
        Get a strategy instance by ID.
        
        Args:
            strategy_id: Strategy identifier
            config_override: Optional configuration override
            
        Returns:
            ExtensiblePersonaStrategy instance
        """
        # Check cache
        cache_key = f"{strategy_id}_{hash(str(config_override)) if config_override else 'default'}"
        if cache_key in self.strategy_instances:
            return self.strategy_instances[cache_key]
        
        # Get configuration
        if strategy_id not in self.strategy_configs:
            self.logger.warning(f"Strategy {strategy_id} not found, using configurable strategy")
            config = config_override or {"name": strategy_id}
        else:
            config = self.strategy_configs[strategy_id].copy()
            if config_override:
                config.update(config_override)
        
        # Get strategy class
        strategy_class = self._get_strategy_class(config)
        
        # Create instance
        strategy_instance = strategy_class(config)
        
        # Cache instance
        self.strategy_instances[cache_key] = strategy_instance
        
        return strategy_instance
    
    def _get_strategy_class(self, config: Dict[str, Any]) -> Type[ExtensiblePersonaStrategy]:
        """Get strategy class from configuration."""
        # Check if custom class is specified
        custom_class = config.get("strategy_class")
        if custom_class:
            try:
                module_path, class_name = custom_class.rsplit(".", 1)
                module = importlib.import_module(module_path)
                strategy_class = getattr(module, class_name)
                
                if issubclass(strategy_class, ExtensiblePersonaStrategy):
                    return strategy_class
                    
            except Exception as e:
                self.logger.error(f"Error loading custom strategy class {custom_class}: {e}")
        
        # Use default configurable strategy
        return ConfigurablePersonaStrategy
    
    def register_strategy_config(self, strategy_id: str, config: Dict[str, Any]) -> None:
        """Register a strategy configuration programmatically."""
        self.strategy_configs[strategy_id] = config
        
        # Clear cache for this strategy
        keys_to_remove = [key for key in self.strategy_instances.keys() if key.startswith(f"{strategy_id}_")]
        for key in keys_to_remove:
            del self.strategy_instances[key]
        
        self.logger.info(f"Registered strategy configuration: {strategy_id}")
    
    def list_strategies(self) -> List[str]:
        """List all available strategy IDs."""
        return list(self.strategy_configs.keys())
    
    def reload_configurations(self) -> None:
        """Reload all strategy configurations."""
        self.strategy_configs.clear()
        self.strategy_instances.clear()
        self._load_strategy_configurations()
        self.logger.info("Reloaded all strategy configurations")


# Global registry instance
extensible_strategy_registry = ExtensibleStrategyRegistry()
