# Concierge Agent Configuration for LangGraph System
# Updated to use UnifiedPersonaNode with proper configuration

# Basic persona information
id: "concierge-agent"
persona_id: "concierge"
name: "Datagenius Concierge"
description: "Your knowledgeable guide to Datagenius AI personas with conversational assistance and advanced coordination capabilities"
version: "3.1.0"
author: "Datagenius Team"
agent_class: "agents.langgraph.nodes.unified_persona_node.UnifiedPersonaNode"
agent_type: "concierge"
industry: "Technology"
skills:
  - "Conversational Assistance"
  - "Question Answering"
  - "Guidance"
  - "Persona Recommendation"
  - "Data Assistance"
  - "Workflow Coordination"
rating: 4.9
review_count: 150
image_url: "/placeholder.svg"

# Unified persona system - no legacy compatibility

# Strategy configuration
strategy_id: "concierge"
strategy_class: "agents.langgraph.strategies.extensible_strategy_system.ConfigurablePersonaStrategy"

# Capabilities (dynamically loaded)
capabilities:
  - "persona_recommendation"
  - "intent_analysis"
  - "conversation_management"
  - "data_attachment_assistance"
  - "workflow_coordination"
  - "user_guidance"
  - "business_context_awareness"
  - "intelligent_guidance"
  - "llm_based_understanding"
  - "intelligent_routing"
  - "context_management"
  - "error_handling"
  - "performance_monitoring"
  - "conversation_persistence"
  - "user_preference_learning"
  - "multi_modal_support"
  - "real_time_adaptation"

# Intent interpretation (LLM-driven, no hardcoded values)
intent_interpretation:
  enable_dynamic_intent_detection: true
  use_llm_for_intent_analysis: true
  intent_confidence_threshold: 0.6
  fallback_to_capability_matching: true
  enable_persona_recommendation_logic: true

# Tools configuration (migrated from legacy agent)
tools:
  # Core concierge tools
  - "persona_marketplace"
  - "data_access"
  - "conversation_management"
  - "user_feedback"
  # Enhanced context and recommendation tools
  - "enhanced_context_manager"
  - "persona_recommender"
  - "data_attachment_assistant"
  - "persona_routing"

# Legacy tool indicators (migrated from get_tool_indicators)
tool_indicators:
  - "persona_recommendation_request"
  - "persona_selection"
  - "concierge_task"

# Legacy conversational flags (migrated from get_conversational_flags)
conversational_flags:
  - "skip_persona_recommendation"
  - "is_conversational"
  - "recommendation_completed"
  - "tool_completed"
  - "auto_conversational_mode"

# Legacy new request patterns (migrated from _get_agent_specific_new_request_patterns)
new_request_patterns:
  - "recommend personas"
  - "suggest agents"
  - "help me choose"
  - "show me options"
  - "what personas"
  - "which agent should"
  - "persona recommendations"
  - "agent suggestions"
  - "recommend"
  - "suggest"

# Processing rules (completely configurable)
processing_rules:
  # Capability scoring rules
  capability_scoring:
    keyword_analysis:
      type: "keyword_match"
      keywords: ["help", "guide", "recommend", "suggest", "persona", "agent"]
      score_per_match: 0.15
    
    guidance_context:
      type: "capability_match"
      capabilities: ["persona_recommendation", "user_guidance"]
      score_per_match: 0.25
  
  # Processing pipeline (configurable workflow)
  processing_pipeline:
    - name: "intent_analysis"
      type: "context_extraction"
      extraction_rules:
        user_intent:
          type: "state_lookup"
          key: "user_intent"
          default: "general_inquiry"
        
        conversation_stage:
          type: "state_lookup"
          key: "conversation_stage"
          default: "initial"
        
        persona_preferences:
          type: "state_lookup"
          key: "persona_preferences"
          default: {}
    
    - name: "persona_recommendation_logic"
      type: "recommendation_engine"
      engine:
        type: "intelligent_matching"
        factors:
          - "user_intent"
          - "conversation_history"
          - "business_context"
          - "previous_interactions"
        threshold: 0.7
    
    - name: "response_preparation"
      type: "state_update"
      updates:
        guidance_ready: true
        recommendations_prepared: true
        execution_timestamp: "${current_timestamp}"

# Prompt templates (migrated from legacy system prompts)
prompt_templates:
  system_prompt: |
    You are the Datagenius Concierge, a knowledgeable and helpful AI assistant. Your primary role is to help users navigate Datagenius and find the right AI personas for their tasks, but you are also capable of providing helpful information and engaging in meaningful conversations.

    IMPORTANT: You are an AI assistant helping the user. Always respond FROM the perspective of the AI assistant TO the user, never as if you are the user.

    Your core responsibilities:
    1. **Persona Recommendations**: Help users find the right AI specialist for their specific needs
    2. **Platform Guidance**: Guide users through Datagenius features and capabilities
    3. **General Assistance**: Provide helpful information and support for various inquiries
    4. **Workflow Coordination**: Help coordinate interactions between different AI personas

    Your current context:
    - User Intent: {user_intent}
    - Conversation Stage: {conversation_stage}
    - Business Profile: {business_name}
    - Industry: {industry}

    Your approach should be:
    - Answer questions when you can provide value
    - Be genuinely helpful and informative
    - Then offer specialized persona connections for deeper expertise
    - Guide users toward Datagenius capabilities when appropriate
    - Show awareness of previous conversation when continuing a discussion

    Available personas to recommend:
    - **Composable Analyst**: For data analysis, visualization, and statistical insights
    - **Composable Marketer**: For marketing strategy, content creation, and campaign planning
    - **Classification Agent**: For organizing, categorizing, and classifying content

    Always maintain a helpful, professional tone while being approachable and conversational.
  
  greeting_prompt: |
    Hello! I'm your **Datagenius Concierge** - your intelligent guide to finding the perfect AI specialist for your needs.

    **🎯 Quick Persona Recommendations:**
    [📊 Data Analysis](action:recommend_analyst) [📈 Marketing](action:recommend_marketer) [🗂️ Classification](action:recommend_classifier)

    **How can I help you today?**

    **🚀 Popular Tasks:**
    [📋 Upload Data](action:data_upload_help) [💡 Get Started](action:platform_tour) [❓ Ask Questions](action:general_help)
  
  persona_recommendation_prompt: |
    Based on your needs, I recommend connecting with the **{recommended_persona}**.

    **Why this persona is perfect for you:**
    {recommendation_reasons}

    **What they can help with:**
    {persona_capabilities}

    Would you like me to connect you with the {recommended_persona} now, or would you prefer to explore other options?
  
  error_prompt: |
    I apologize, but I encountered an issue while trying to help you:
    
    Error: {error_message}
    
    Let me try a different approach or connect you with a specialized persona who can better assist with your request.

# Components configuration (migrated from legacy YAML configs)
components:
  - type: "enhanced_context_manager"
    name: "EnhancedContextManager"
    config:
      context_ttl: 3600
      max_history_size: 15
      sync_interval: 300
      enable_entity_tracking: true
      enable_context_versioning: true
      enable_compression: true

  - type: "persona_recommender"
    name: "PersonaRecommender"
    config:
      recommendation_threshold: 0.7
      max_recommendations: 5
      consider_user_history: true
      enable_confidence_boosting: true
      cache_ttl: 300

  - type: "data_attachment_assistant"
    name: "DataAttachmentAssistant"
    config:
      supported_file_types: ["csv", "xlsx", "pdf", "docx", "txt", "json"]
      max_file_size: "100MB"
      enable_preview: true
      auto_detect_format: true

  - type: "persona_routing"
    name: "PersonaRouter"
    config:
      routing_threshold: 0.7
      enable_intelligent_routing: true
      fallback_persona: "concierge"

  - type: "mcp_server"
    name: "MCPServer"
    config:
      enable_auto_tools: true
      tool_selection_threshold: 0.5
      max_tool_calls: 10
      tool_timeout: 30
      available_tools:
        - "persona_marketplace"
        - "data_access"
        - "conversation_management"
        - "user_feedback"

# Persona descriptions for recommendations (migrated from legacy configs)
persona_descriptions:
  analysis:
    name: "Composable Analyst"
    description: "Expert in data analysis, visualization, and statistical insights"
    capabilities: ["data_analysis", "visualization", "statistical_analysis", "reporting"]
    keywords: ["data", "analysis", "chart", "graph", "statistics", "visualization"]
    priority: 1
    
  marketing:
    name: "Composable Marketer"
    description: "Marketing specialist for strategy, content creation, and campaigns"
    capabilities: ["marketing_strategy", "content_creation", "campaign_planning", "social_media"]
    keywords: ["marketing", "campaign", "content", "brand", "promotion", "strategy"]
    priority: 2
    
  classification:
    name: "Classification Specialist"
    description: "Expert in organizing, categorizing, and classifying content"
    capabilities: ["text_classification", "content_organization", "data_categorization"]
    keywords: ["classify", "organize", "categorize", "sort", "label", "tag"]
    priority: 3

# Strategy-specific configuration
strategy_config:
  enable_intelligent_routing: true
  enable_context_awareness: true
  enable_learning_from_interactions: true
  
  # Recommendation preferences
  recommendation_strategy: "intent_based"
  confidence_threshold: 0.7
  max_recommendations_per_request: 3
  
  # Conversation management
  conversation_memory_duration: 3600  # 1 hour
  enable_conversation_continuity: true
  track_user_preferences: true

# Integration settings
integrations:
  # Business profile integration
  business_profile:
    enable_industry_context: true
    enable_company_context: true
    context_fields:
      - "industry"
      - "company_size"
      - "business_goals"
      - "user_preferences"
  
  # Cross-agent intelligence
  cross_agent_intelligence:
    enable_knowledge_sharing: true
    coordinate_with_all_personas: true
    share_user_context: true
    enable_handoff_coordination: true

# Performance settings
performance:
  enable_caching: true
  cache_recommendations: true
  cache_duration_minutes: 30
  
  response_optimization:
    enable_fast_recommendations: true
    preload_common_personas: true
  
  timeouts:
    recommendation_timeout_seconds: 10
    persona_connection_timeout_seconds: 30

# Monitoring and metrics
monitoring:
  enable_performance_tracking: true
  track_recommendation_accuracy: true
  track_user_satisfaction: true
  
  metrics_to_collect:
    - "recommendation_response_time"
    - "persona_connection_success_rate"
    - "user_satisfaction_score"
    - "conversation_completion_rate"
