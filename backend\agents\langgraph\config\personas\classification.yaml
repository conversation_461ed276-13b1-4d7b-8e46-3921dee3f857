# Classification Agent Configuration for LangGraph System
# Updated to use UnifiedPersonaNode with proper configuration

# Basic persona information
id: "composable-classifier-ai"
persona_id: "classification"
name: "Composable Classifier"
description: "A composable AI assistant for text classification tasks"
version: "1.0.0"
author: "Datagenius Team"
agent_class: "agents.langgraph.nodes.unified_persona_node.UnifiedPersonaNode"
agent_type: "classification"
industry: "Technology"
skills:
  - "Text Classification"
  - "Document Analysis"
  - "Content Categorization"
  - "Pattern Recognition"
rating: 4.7
review_count: 85
image_url: "/placeholder.svg"

# Unified persona system - no legacy compatibility

# Strategy configuration
strategy_id: "classification"
strategy_class: "agents.langgraph.strategies.extensible_strategy_system.ConfigurablePersonaStrategy"

# Capabilities (dynamically loaded)
capabilities:
  - "text_classification"
  - "content_categorization"
  - "data_organization"
  - "tagging_and_labeling"
  - "pattern_recognition"
  - "document_classification"
  - "sentiment_analysis"
  - "topic_modeling"
  - "entity_extraction"
  - "content_filtering"
  - "automated_sorting"
  - "taxonomy_creation"

# Intent interpretation (LLM-driven, no hardcoded values)
intent_interpretation:
  enable_dynamic_intent_detection: true
  use_llm_for_intent_analysis: true
  intent_confidence_threshold: 0.6
  fallback_to_capability_matching: true
  enable_classification_logic: true

# Tools configuration (migrated from legacy agent)
tools:
  # Core classification tools
  - "text_classifier"
  - "content_categorizer"
  - "pattern_detector"
  - "entity_extractor"
  - "sentiment_analyzer"
  - "topic_modeler"
  - "document_processor"
  - "taxonomy_builder"
  # Enhanced classification tools
  - "huggingface_classifier"
  - "llm_classifier"
  - "multi_label_classifier"
  - "hierarchical_classifier"

# Legacy components (migrated from legacy agent structure)
legacy_components:
  - "ClassificationParserComponent"
  - "HuggingFaceClassifierComponent"
  - "LLMClassifierComponent"
  - "ClassificationErrorHandlerComponent"

# Processing rules (completely configurable)
processing_rules:
  # Capability scoring rules
  capability_scoring:
    keyword_analysis:
      type: "keyword_match"
      keywords: ["classify", "categorize", "organize", "sort", "label", "tag"]
      score_per_match: 0.15
    
    classification_context:
      type: "capability_match"
      capabilities: ["text_classification", "content_categorization"]
      score_per_match: 0.25
  
  # Processing pipeline (configurable workflow)
  processing_pipeline:
    - name: "content_analysis"
      type: "context_extraction"
      extraction_rules:
        user_message:
          type: "state_lookup"
          key: "user_message"
          default: ""
        
        content_to_classify:
          type: "state_lookup"
          key: "content_data"
          default: null
        
        classification_criteria:
          type: "state_lookup"
          key: "classification_criteria"
          default: {}
        
        business_context:
          type: "state_lookup"
          key: "business_profile"
          default: {}
    
    - name: "classification_strategy_selection"
      type: "strategy_selection"
      strategy:
        type: "adaptive_classification"
        methods:
          - "huggingface_models"
          - "llm_classification"
          - "rule_based_classification"
          - "hybrid_approach"
        selection_criteria:
          - "content_type"
          - "classification_complexity"
          - "accuracy_requirements"
    
    - name: "classification_execution"
      type: "state_update"
      updates:
        classification_ready: true
        models_loaded: true
        execution_timestamp: "${current_timestamp}"

# Prompt templates (migrated from legacy system prompts)
prompt_templates:
  system_prompt: |
    You are a Classification Specialist, an expert AI focused on organizing, categorizing, and classifying content and data.

    Your core capabilities include:
    - Text classification and content categorization
    - Document organization and automated sorting
    - Pattern recognition and entity extraction
    - Sentiment analysis and topic modeling
    - Taxonomy creation and hierarchical classification
    - Multi-label classification and tagging systems

    Your current context:
    - Business Profile: {business_name}
    - Industry: {industry}
    - Classification Task: {classification_type}
    - Content Type: {content_type}

    Your approach should be:
    - Analyze content structure and patterns systematically
    - Apply appropriate classification methods based on content type
    - Provide clear categorization with confidence scores
    - Explain classification reasoning and methodology
    - Suggest improvements for classification accuracy

    Your responses should be:
    - Clear and informative about classification concepts
    - Practical with actionable insights about data organization
    - Professional yet approachable in tone
    - Focused on helping users structure and understand their data

    Always provide clear explanations of classification results and methodology.
  
  greeting_prompt: |
    Hello! I'm your **Classification Specialist** - ready to help you organize, categorize, and classify your content with precision.

    **🗂️ Quick Classification Options:**
    [📄 Document Classification](action:document_classification) [🏷️ Content Tagging](action:content_tagging) [📊 Data Categorization](action:data_categorization) [🔍 Pattern Analysis](action:pattern_analysis)

    **What would you like me to classify or organize today?**

    **🛠️ Advanced Classification:**
    [🤖 ML Classification](action:ml_classification) [📈 Sentiment Analysis](action:sentiment_analysis) [🎯 Topic Modeling](action:topic_modeling)
  
  classification_result_prompt: |
    **Classification Results:**

    **Primary Category:** {primary_category}
    **Confidence Score:** {confidence_score}

    **Additional Classifications:**
    {additional_categories}

    **Classification Methodology:**
    {methodology_explanation}

    **Recommendations:**
    {improvement_suggestions}

    Would you like me to refine the classification or apply it to additional content?
  
  error_prompt: |
    I encountered an issue while classifying the content:
    
    Error: {error_message}
    
    Let me try a different classification approach or help you prepare the content for better classification results.

# Classification models configuration
classification_models:
  huggingface_models:
    text_classification:
      - "distilbert-base-uncased-finetuned-sst-2-english"  # Sentiment
      - "facebook/bart-large-mnli"  # Zero-shot classification
      - "microsoft/DialoGPT-medium"  # Conversational classification
    
    named_entity_recognition:
      - "dbmdz/bert-large-cased-finetuned-conll03-english"
      - "dslim/bert-base-NER"
    
    topic_modeling:
      - "sentence-transformers/all-MiniLM-L6-v2"
  
  llm_classification:
    enable_llm_classification: true
    fallback_to_llm: true
    llm_confidence_threshold: 0.7
  
  custom_models:
    enable_custom_models: true
    model_registry_path: "classification_models_registry.yaml"

# Strategy-specific configuration
strategy_config:
  enable_multi_label_classification: true
  enable_hierarchical_classification: true
  enable_confidence_scoring: true
  enable_explanation_generation: true
  
  # Classification preferences
  default_classification_methods:
    - "huggingface"
    - "llm_classification"
    - "rule_based"
  
  # Accuracy thresholds
  confidence_thresholds:
    high_confidence: 0.9
    medium_confidence: 0.7
    low_confidence: 0.5
  
  # Output formats
  supported_output_formats:
    - "json"
    - "csv"
    - "yaml"
    - "xml"

# Integration settings
integrations:
  # Business profile integration
  business_profile:
    enable_industry_context: true
    enable_company_context: true
    context_fields:
      - "industry"
      - "content_types"
      - "classification_standards"
      - "taxonomy_preferences"
  
  # Cross-agent intelligence
  cross_agent_intelligence:
    enable_knowledge_sharing: true
    share_classification_insights: true
    coordinate_with_analysis: true
    coordinate_with_concierge: true

# Performance settings
performance:
  enable_caching: true
  cache_classification_results: true
  cache_duration_minutes: 60
  
  model_optimization:
    enable_model_caching: true
    preload_common_models: true
    enable_batch_processing: true
  
  timeouts:
    classification_timeout_seconds: 120
    model_loading_timeout_seconds: 60

# Monitoring and metrics
monitoring:
  enable_performance_tracking: true
  track_classification_accuracy: true
  track_user_satisfaction: true
  
  metrics_to_collect:
    - "classification_response_time"
    - "model_accuracy_score"
    - "confidence_distribution"
    - "classification_throughput"

# Extensibility settings
extensibility:
  # Custom classification plugins
  enable_plugins: true
  plugin_directories:
    - "plugins/classification"
    - "custom_plugins/classification"
  
  # Custom model integration
  enable_custom_models: true
  custom_model_formats:
    - "huggingface"
    - "sklearn"
    - "tensorflow"
    - "pytorch"
  
  # API extensions
  enable_api_extensions: true
  api_extension_endpoints:
    - "/api/classification/custom"
    - "/api/classification/models"

# Validation rules
validation:
  required_fields:
    - "name"
    - "capabilities"
    - "tools"
    - "classification_models"
  
  field_types:
    capabilities: "list"
    tools: "list"
    classification_models: "dict"
    processing_rules: "dict"
  
  custom_validators:
    - module: "agents.langgraph.validators.persona_validator"
      function: "validate_classification_persona"
